
[certs]

[docs]
sphinx
jaraco.packaging>=8.2
rst.linker>=1.9
jaraco.tidelift>=1.4
pygments-github-lexers==0.0.5
sphinx-inline-tabs
sphinxcontrib-towncrier
furo

[ssl]

[testing]
pytest>=6
pytest-checkdocs>=2.4
pytest-flake8
pytest-cov
pytest-enabler>=1.0.1
mock
flake8-2020
virtualenv>=13.0.0
pytest-virtualenv>=1.2.7
wheel
paver
pip>=19.1
jaraco.envs>=2.2
pytest-xdist
sphinx
jaraco.path>=3.2.0

[testing:platform_python_implementation != "PyPy"]
pytest-black>=0.3.7
pytest-mypy
