# -*- coding: utf-8 -*-

# Author: <PERSON> <<EMAIL>>
#         <PERSON> <<EMAIL>>
#         (mostly translation, see implementation details)
# License: BSD 3 clause

"""
The :mod:`sklearn.gaussian_process` module implements Gaussian Process
based regression and classification.
"""

from ._gpr import GaussianProcessRegressor
from ._gpc import GaussianProcessClassifier
from . import kernels


__all__ = ['GaussianProcessRegressor', 'GaussianProcessClassifier',
           'kernels']
